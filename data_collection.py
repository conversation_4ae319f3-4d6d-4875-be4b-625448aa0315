import pandas as pd
import numpy as np
import requests
import datetime
import time
from typing import Optional

def get_ethusdt_data(
    ticker: str = 'ETHUSDT', 
    interval: str = '1d', 
    start: str = '2020-01-01 00:00:00', 
    end: Optional[str] = None
) -> pd.DataFrame:
    """
    Lấy dữ liệu ETHUSDT từ Binance API
    
    Args:
        ticker: Symbol trading (mặc định ETHUSDT)
        interval: <PERSON>hung thời gian (1d, 4h, 1h, etc.)
        start: Thời gian bắt đầu
        end: Thờ<PERSON> gian kết thúc (None = hiện tại)
    
    Returns:
        DataFrame với dữ liệu OHLCV
    """
    if end is None:
        end = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    columns = [
        'open_time', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'qav', 'num_trades', 'taker_base_vol', 
        'taker_quote_vol', 'ignore'
    ]
    
    usecols = ['open', 'high', 'low', 'close', 'volume', 'qav', 'num_trades']
    
    start_ts = int(datetime.datetime.timestamp(pd.to_datetime(start)) * 1000)
    end_ts = int(datetime.datetime.timestamp(pd.to_datetime(end)) * 1000)
    
    df = pd.DataFrame()
    current_start = start_ts
    
    print(f'Downloading {interval} {ticker} data from {start} to {end}...')
    
    while current_start < end_ts:
        try:
            url = f'https://api.binance.com/api/v3/klines'
            params = {
                'symbol': ticker,
                'interval': interval,
                'limit': 1000,
                'startTime': current_start,
                'endTime': end_ts
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = pd.DataFrame(response.json(), columns=columns, dtype=np.float64)
            
            if data.empty:
                break
                
            # Convert timestamps to datetime
            data.index = pd.to_datetime(data.open_time, unit='ms')
            data = data[usecols]
            
            df = pd.concat([df, data], axis=0)
            
            # Update start time for next batch
            if len(data) > 0:
                current_start = int(data.index[-1].timestamp() * 1000) + 86400000  # Add 1 day in milliseconds
            else:
                break
            
            print(f'Downloaded {len(data)} records, total: {len(df)}')
            
            # Rate limiting
            time.sleep(0.1)
            
        except Exception as e:
            print(f'Error downloading data: {e}')
            time.sleep(1)
            continue
    
    # Remove duplicates and sort
    df = df[~df.index.duplicated(keep='first')].sort_index()
    
    # Rename columns for clarity
    df = df.rename(columns={
        'qav': 'quote_asset_volume',
        'num_trades': 'number_of_trades'
    })
    
    print(f'Data collection complete. Total records: {len(df)}')
    print(f'Date range: {df.index.min()} to {df.index.max()}')
    
    return df[['open', 'high', 'low', 'close', 'volume']]

def split_train_test_data(df: pd.DataFrame, split_date: str = '2023-01-01') -> tuple:
    """
    Chia dữ liệu thành train và test sets
    
    Args:
        df: DataFrame chứa dữ liệu
        split_date: Ngày chia dữ liệu
    
    Returns:
        Tuple (train_data, test_data)
    """
    split_timestamp = pd.to_datetime(split_date)
    
    train_data = df[df.index < split_timestamp].copy()
    test_data = df[df.index >= split_timestamp].copy()
    
    print(f"Train data: {len(train_data)} records ({train_data.index.min()} to {train_data.index.max()})")
    print(f"Test data: {len(test_data)} records ({test_data.index.min()} to {test_data.index.max()})")
    
    return train_data, test_data

if __name__ == "__main__":
    # Thu thập dữ liệu ETHUSDT
    df = get_ethusdt_data()
    
    # Lưu dữ liệu
    df.to_csv('ethusdt_data.csv')
    print("Data saved to ethusdt_data.csv")
    
    # Chia dữ liệu train/test
    train_data, test_data = split_train_test_data(df)
    
    # Lưu dữ liệu train/test riêng biệt
    train_data.to_csv('ethusdt_train.csv')
    test_data.to_csv('ethusdt_test.csv')
    
    print("Train data saved to ethusdt_train.csv")
    print("Test data saved to ethusdt_test.csv")
