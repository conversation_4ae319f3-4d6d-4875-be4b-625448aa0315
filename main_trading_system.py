import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import custom modules
from data_collection import get_ethusdt_data, split_train_test_data
from technical_indicators import calculate_all_indicators
from backtesting_engine import BacktestingEngine
from trading_strategies import get_strategy_function, optimize_strategy_parameters

class TradingSystemManager:
    """
    Quản lý toàn bộ hệ thống trading
    """
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.data = None
        self.train_data = None
        self.test_data = None
        self.best_strategy = None
        self.best_params = None
        self.results = {}
    
    def load_and_prepare_data(self):
        """Thu thập và chuẩn bị dữ liệu"""
        print("=== LOADING AND PREPARING DATA ===")

        # Load data
        try:
            # Try to load existing extended data first
            self.data = pd.read_csv('ethusdt_extended_data.csv', index_col=0, parse_dates=True)
            print("Loaded existing data from ethusdt_extended_data.csv")
        except FileNotFoundError:
            try:
                # Try sample data
                self.data = pd.read_csv('ethusdt_sample_data.csv', index_col=0, parse_dates=True)
                print("Loaded sample data from ethusdt_sample_data.csv")
            except FileNotFoundError:
                print("No data files found. Please run simple_data_loader.py first.")
                return
        
        # Calculate technical indicators
        print("Calculating technical indicators...")
        self.data = calculate_all_indicators(self.data)
        
        # Split train/test data
        self.train_data, self.test_data = split_train_test_data(self.data, '2023-01-01')
        
        print(f"Data preparation complete:")
        print(f"- Total records: {len(self.data)}")
        print(f"- Train period: {self.train_data.index.min()} to {self.train_data.index.max()}")
        print(f"- Test period: {self.test_data.index.min()} to {self.test_data.index.max()}")
    
    def test_strategies(self):
        """Test các chiến lược khác nhau trên dữ liệu train"""
        print("\n=== TESTING STRATEGIES ON TRAIN DATA ===")
        
        strategies_to_test = [
            'trend_following_ema',
            'mean_reversion_bollinger', 
            'momentum_macd_rsi',
            'breakout_strategy',
            'multi_timeframe_strategy',
            'adaptive_strategy'
        ]
        
        strategy_results = {}
        
        for strategy_name in strategies_to_test:
            print(f"\nTesting {strategy_name}...")
            
            engine = BacktestingEngine(self.initial_capital)
            strategy_func = get_strategy_function(strategy_name)
            
            try:
                performance = engine.run_backtest(self.train_data, strategy_func)
                strategy_results[strategy_name] = performance
                
                print(f"Results for {strategy_name}:")
                print(f"  Total Return: {performance['total_return']:.2f}%")
                print(f"  Sharpe Ratio: {performance['sharpe_ratio']:.2f}")
                print(f"  Max Drawdown: {performance['max_drawdown']:.2f}%")
                print(f"  Win Rate: {performance['win_rate']:.2f}%")
                
            except Exception as e:
                print(f"Error testing {strategy_name}: {e}")
                continue
        
        self.results['strategy_comparison'] = strategy_results
        return strategy_results
    
    def optimize_best_strategy(self):
        """Tối ưu hóa tham số cho chiến lược tốt nhất"""
        print("\n=== OPTIMIZING BEST STRATEGY ===")
        
        # Find best strategy based on Sharpe ratio
        best_strategy_name = None
        best_sharpe = -float('inf')
        
        for strategy_name, performance in self.results['strategy_comparison'].items():
            if performance['sharpe_ratio'] > best_sharpe:
                best_sharpe = performance['sharpe_ratio']
                best_strategy_name = strategy_name
        
        print(f"Best strategy: {best_strategy_name} (Sharpe: {best_sharpe:.2f})")
        
        # Define parameter ranges for optimization
        parameter_ranges = {
            'trend_following_ema': {
                'fast_period': [8, 12, 16],
                'slow_period': [21, 26, 30],
                'rsi_period': [14, 21],
                'rsi_oversold': [25, 30, 35],
                'rsi_overbought': [65, 70, 75]
            },
            'mean_reversion_bollinger': {
                'bb_period': [15, 20, 25],
                'bb_std': [1.5, 2.0, 2.5],
                'rsi_period': [14, 21],
                'rsi_oversold': [25, 30, 35],
                'rsi_overbought': [65, 70, 75]
            },
            'momentum_macd_rsi': {
                'macd_fast': [8, 12, 16],
                'macd_slow': [21, 26, 30],
                'macd_signal': [7, 9, 11],
                'rsi_period': [14, 21],
                'rsi_threshold': [45, 50, 55]
            },
            'breakout_strategy': {
                'lookback_period': [15, 20, 25],
                'volume_threshold': [1.2, 1.5, 2.0],
                'atr_multiplier': [1.0, 1.5, 2.0]
            },
            'multi_timeframe_strategy': {
                'short_ma': [8, 10, 12],
                'long_ma': [45, 50, 55],
                'rsi_period': [14, 21],
                'stoch_k': [12, 14, 16],
                'stoch_d': [3, 5]
            },
            'adaptive_strategy': {
                'volatility_threshold': [0.015, 0.02, 0.025],
                'trend_strength_period': [15, 20, 25]
            }
        }
        
        if best_strategy_name in parameter_ranges:
            print(f"Optimizing parameters for {best_strategy_name}...")
            
            optimization_result = optimize_strategy_parameters(
                self.train_data, 
                best_strategy_name, 
                parameter_ranges[best_strategy_name]
            )
            
            self.best_strategy = best_strategy_name
            self.best_params = optimization_result['best_params']
            
            print(f"Best parameters: {self.best_params}")
            print(f"Best performance: {optimization_result['best_performance']:.2f}")
            
            self.results['optimization'] = optimization_result
        
        return self.best_strategy, self.best_params
    
    def test_on_out_of_sample(self):
        """Test chiến lược tối ưu trên dữ liệu test"""
        print("\n=== TESTING ON OUT-OF-SAMPLE DATA ===")
        
        if not self.best_strategy or not self.best_params:
            print("No optimized strategy found. Using default parameters.")
            self.best_strategy = 'multi_timeframe_strategy'
            self.best_params = {}
        
        engine = BacktestingEngine(self.initial_capital)
        strategy_func = get_strategy_function(self.best_strategy)
        
        # Test on out-of-sample data
        test_performance = engine.run_backtest(self.test_data, strategy_func, **self.best_params)
        
        print(f"Out-of-sample results for {self.best_strategy}:")
        print(f"  Total Return: {test_performance['total_return']:.2f}%")
        print(f"  Annualized Return: {test_performance['annualized_return']:.2f}%")
        print(f"  Sharpe Ratio: {test_performance['sharpe_ratio']:.2f}")
        print(f"  Max Drawdown: {test_performance['max_drawdown']:.2f}%")
        print(f"  Win Rate: {test_performance['win_rate']:.2f}%")
        print(f"  Total Trades: {test_performance['total_trades']}")
        print(f"  Buy & Hold Return: {test_performance['buy_hold_return']:.2f}%")
        print(f"  Excess Return: {test_performance['excess_return']:.2f}%")
        print(f"  Final Portfolio Value: ${test_performance['final_portfolio_value']:,.2f}")
        
        self.results['test_performance'] = test_performance
        return test_performance
    
    def generate_report(self):
        """Tạo báo cáo chi tiết"""
        print("\n=== GENERATING DETAILED REPORT ===")
        
        report = {
            'strategy_name': self.best_strategy,
            'optimized_parameters': self.best_params,
            'test_results': self.results.get('test_performance', {}),
            'initial_capital': self.initial_capital,
            'data_period': {
                'train_start': str(self.train_data.index.min()),
                'train_end': str(self.train_data.index.max()),
                'test_start': str(self.test_data.index.min()),
                'test_end': str(self.test_data.index.max())
            }
        }
        
        # Save report
        import json
        with open('trading_strategy_report.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print("Report saved to trading_strategy_report.json")
        return report
    
    def plot_results(self):
        """Vẽ biểu đồ kết quả"""
        if 'test_performance' not in self.results:
            print("No test results to plot")
            return
        
        portfolio_series = self.results['test_performance']['portfolio_series']
        
        plt.figure(figsize=(15, 10))
        
        # Portfolio value over time
        plt.subplot(2, 2, 1)
        portfolio_series.plot(title='Portfolio Value Over Time')
        plt.ylabel('Portfolio Value ($)')
        plt.grid(True)
        
        # Price vs Portfolio performance
        plt.subplot(2, 2, 2)
        price_normalized = (self.test_data['close'] / self.test_data['close'].iloc[0]) * self.initial_capital
        portfolio_normalized = portfolio_series
        
        plt.plot(price_normalized.index, price_normalized.values, label='Buy & Hold', alpha=0.7)
        plt.plot(portfolio_normalized.index, portfolio_normalized.values, label='Strategy', alpha=0.7)
        plt.title('Strategy vs Buy & Hold')
        plt.ylabel('Value ($)')
        plt.legend()
        plt.grid(True)
        
        # Drawdown
        plt.subplot(2, 2, 3)
        rolling_max = portfolio_series.expanding().max()
        drawdown = (portfolio_series - rolling_max) / rolling_max * 100
        drawdown.plot(title='Drawdown', color='red')
        plt.ylabel('Drawdown (%)')
        plt.grid(True)
        
        # Returns distribution
        plt.subplot(2, 2, 4)
        returns = portfolio_series.pct_change().dropna()
        returns.hist(bins=50, alpha=0.7)
        plt.title('Returns Distribution')
        plt.xlabel('Daily Returns')
        plt.ylabel('Frequency')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('trading_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("Charts saved to trading_results.png")

def main():
    """Hàm chính chạy toàn bộ hệ thống"""
    print("ETHEREUM TRADING STRATEGY SYSTEM")
    print("=" * 50)
    
    # Initialize system
    system = TradingSystemManager(initial_capital=100000)
    
    # Step 1: Load and prepare data
    system.load_and_prepare_data()
    
    # Step 2: Test strategies
    system.test_strategies()
    
    # Step 3: Optimize best strategy
    system.optimize_best_strategy()
    
    # Step 4: Test on out-of-sample data
    system.test_on_out_of_sample()
    
    # Step 5: Generate report
    system.generate_report()
    
    # Step 6: Plot results
    system.plot_results()
    
    print("\n" + "=" * 50)
    print("TRADING SYSTEM ANALYSIS COMPLETE")
    print("=" * 50)

if __name__ == "__main__":
    main()
