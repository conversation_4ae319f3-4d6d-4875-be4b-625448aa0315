import pandas as pd
import numpy as np
from typing import Tuple

class TechnicalIndicators:
    """
    <PERSON><PERSON><PERSON> t<PERSON>h to<PERSON> c<PERSON>c chỉ báo kỹ thuật cho trading
    """
    
    @staticmethod
    def sma(data: pd.Series, window: int) -> pd.Series:
        """Simple Moving Average"""
        return data.rolling(window=window).mean()
    
    @staticmethod
    def ema(data: pd.Series, window: int) -> pd.Series:
        """Exponential Moving Average"""
        return data.ewm(span=window, adjust=False).mean()
    
    @staticmethod
    def rsi(data: pd.Series, window: int = 14) -> pd.Series:
        """Relative Strength Index"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> <PERSON><PERSON>[pd.Series, pd.Series, pd.Series]:
        """MACD (Moving Average Convergence Divergence)"""
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram
    
    @staticmethod
    def bollinger_bands(data: pd.Series, window: int = 20, num_std: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Bollinger Bands"""
        sma = TechnicalIndicators.sma(data, window)
        std = data.rolling(window=window).std()
        upper_band = sma + (std * num_std)
        lower_band = sma - (std * num_std)
        return upper_band, sma, lower_band
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, k_window: int = 14, d_window: int = 3) -> Tuple[pd.Series, pd.Series]:
        """Stochastic Oscillator"""
        lowest_low = low.rolling(window=k_window).min()
        highest_high = high.rolling(window=k_window).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_window).mean()
        return k_percent, d_percent
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """Average True Range"""
        high_low = high - low
        high_close = np.abs(high - close.shift())
        low_close = np.abs(low - close.shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = true_range.rolling(window=window).mean()
        return atr
    
    @staticmethod
    def vwap(high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> pd.Series:
        """Volume Weighted Average Price"""
        typical_price = (high + low + close) / 3
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        return vwap
    
    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """Williams %R"""
        highest_high = high.rolling(window=window).max()
        lowest_low = low.rolling(window=window).min()
        williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
        return williams_r
    
    @staticmethod
    def cci(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 20) -> pd.Series:
        """Commodity Channel Index"""
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(window=window).mean()
        mean_deviation = typical_price.rolling(window=window).apply(
            lambda x: np.abs(x - x.mean()).mean()
        )
        cci = (typical_price - sma_tp) / (0.015 * mean_deviation)
        return cci
    
    @staticmethod
    def momentum(data: pd.Series, window: int = 10) -> pd.Series:
        """Price Momentum"""
        return data.diff(window)
    
    @staticmethod
    def roc(data: pd.Series, window: int = 10) -> pd.Series:
        """Rate of Change"""
        return ((data - data.shift(window)) / data.shift(window)) * 100

def calculate_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    Tính toán tất cả các chỉ báo kỹ thuật cho DataFrame
    
    Args:
        df: DataFrame với columns ['open', 'high', 'low', 'close', 'volume']
    
    Returns:
        DataFrame với tất cả các chỉ báo
    """
    result = df.copy()
    
    # Moving Averages
    result['sma_10'] = TechnicalIndicators.sma(df['close'], 10)
    result['sma_20'] = TechnicalIndicators.sma(df['close'], 20)
    result['sma_50'] = TechnicalIndicators.sma(df['close'], 50)
    result['sma_200'] = TechnicalIndicators.sma(df['close'], 200)
    
    result['ema_10'] = TechnicalIndicators.ema(df['close'], 10)
    result['ema_20'] = TechnicalIndicators.ema(df['close'], 20)
    result['ema_50'] = TechnicalIndicators.ema(df['close'], 50)
    result['ema_200'] = TechnicalIndicators.ema(df['close'], 200)
    
    # RSI
    result['rsi'] = TechnicalIndicators.rsi(df['close'])
    
    # MACD
    macd, signal, histogram = TechnicalIndicators.macd(df['close'])
    result['macd'] = macd
    result['macd_signal'] = signal
    result['macd_histogram'] = histogram
    
    # Bollinger Bands
    bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(df['close'])
    result['bb_upper'] = bb_upper
    result['bb_middle'] = bb_middle
    result['bb_lower'] = bb_lower
    result['bb_width'] = (bb_upper - bb_lower) / bb_middle
    result['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
    
    # Stochastic
    stoch_k, stoch_d = TechnicalIndicators.stochastic(df['high'], df['low'], df['close'])
    result['stoch_k'] = stoch_k
    result['stoch_d'] = stoch_d
    
    # ATR
    result['atr'] = TechnicalIndicators.atr(df['high'], df['low'], df['close'])
    
    # VWAP
    result['vwap'] = TechnicalIndicators.vwap(df['high'], df['low'], df['close'], df['volume'])
    
    # Williams %R
    result['williams_r'] = TechnicalIndicators.williams_r(df['high'], df['low'], df['close'])
    
    # CCI
    result['cci'] = TechnicalIndicators.cci(df['high'], df['low'], df['close'])
    
    # Momentum indicators
    result['momentum_10'] = TechnicalIndicators.momentum(df['close'], 10)
    result['roc_10'] = TechnicalIndicators.roc(df['close'], 10)
    
    # Price position relative to moving averages
    result['price_vs_sma20'] = df['close'] / result['sma_20'] - 1
    result['price_vs_ema20'] = df['close'] / result['ema_20'] - 1
    
    # Volume indicators
    result['volume_sma'] = TechnicalIndicators.sma(df['volume'], 20)
    result['volume_ratio'] = df['volume'] / result['volume_sma']
    
    return result
