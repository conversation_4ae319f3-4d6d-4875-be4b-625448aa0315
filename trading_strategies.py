import pandas as pd
import numpy as np
from typing import Dict, Any

class TradingStrategies:
    """
    <PERSON><PERSON><PERSON> chiế<PERSON> l<PERSON> trading khác nhau
    """
    
    @staticmethod
    def trend_following_ema(df: pd.DataFrame, fast_period: int = 12, slow_period: int = 26, 
                           rsi_period: int = 14, rsi_oversold: int = 30, rsi_overbought: int = 70) -> int:
        """
        Chiến lược trend following sử dụng EMA crossover và RSI filter
        """
        if len(df) < max(fast_period, slow_period, rsi_period):
            return 0
        
        current_row = df.iloc[-1]
        
        # EMA crossover
        ema_fast = current_row[f'ema_{fast_period}']
        ema_slow = current_row[f'ema_{slow_period}']
        
        # RSI filter
        rsi = current_row['rsi']
        
        # Trend direction
        if ema_fast > ema_slow and rsi < rsi_overbought:
            return 1  # Buy signal
        elif ema_fast < ema_slow and rsi > rsi_oversold:
            return -1  # Sell signal
        
        return 0  # Hold
    
    @staticmethod
    def mean_reversion_bollinger(df: pd.DataFrame, bb_period: int = 20, bb_std: float = 2.0,
                                rsi_period: int = 14, rsi_oversold: int = 30, rsi_overbought: int = 70) -> int:
        """
        Chiến lược mean reversion sử dụng Bollinger Bands và RSI
        """
        if len(df) < max(bb_period, rsi_period):
            return 0
        
        current_row = df.iloc[-1]
        
        # Bollinger Bands position
        bb_position = current_row['bb_position']
        rsi = current_row['rsi']
        
        # Mean reversion signals
        if bb_position < 0.1 and rsi < rsi_oversold:  # Price near lower band + oversold
            return 1  # Buy signal
        elif bb_position > 0.9 and rsi > rsi_overbought:  # Price near upper band + overbought
            return -1  # Sell signal
        
        return 0  # Hold
    
    @staticmethod
    def momentum_macd_rsi(df: pd.DataFrame, macd_fast: int = 12, macd_slow: int = 26, macd_signal: int = 9,
                         rsi_period: int = 14, rsi_threshold: int = 50) -> int:
        """
        Chiến lược momentum sử dụng MACD và RSI
        """
        if len(df) < max(macd_slow, rsi_period):
            return 0
        
        current_row = df.iloc[-1]
        prev_row = df.iloc[-2] if len(df) > 1 else current_row
        
        # MACD signals
        macd = current_row['macd']
        macd_signal_line = current_row['macd_signal']
        macd_prev = prev_row['macd']
        macd_signal_prev = prev_row['macd_signal']
        
        # RSI momentum
        rsi = current_row['rsi']
        
        # MACD crossover + RSI confirmation
        if (macd > macd_signal_line and macd_prev <= macd_signal_prev and 
            rsi > rsi_threshold):
            return 1  # Buy signal
        elif (macd < macd_signal_line and macd_prev >= macd_signal_prev and 
              rsi < rsi_threshold):
            return -1  # Sell signal
        
        return 0  # Hold
    
    @staticmethod
    def breakout_strategy(df: pd.DataFrame, lookback_period: int = 20, volume_threshold: float = 1.5,
                         atr_multiplier: float = 1.5) -> int:
        """
        Chiến lược breakout với volume confirmation
        """
        if len(df) < lookback_period:
            return 0
        
        current_row = df.iloc[-1]
        
        # Price breakout
        recent_high = df['high'].iloc[-lookback_period:-1].max()
        recent_low = df['low'].iloc[-lookback_period:-1].min()
        
        current_price = current_row['close']
        volume_ratio = current_row['volume_ratio']
        atr = current_row['atr']
        
        # Breakout conditions
        if (current_price > recent_high and 
            volume_ratio > volume_threshold and
            current_price > recent_high + atr * atr_multiplier):
            return 1  # Buy signal (upward breakout)
        elif (current_price < recent_low and 
              volume_ratio > volume_threshold and
              current_price < recent_low - atr * atr_multiplier):
            return -1  # Sell signal (downward breakout)
        
        return 0  # Hold
    
    @staticmethod
    def multi_timeframe_strategy(df: pd.DataFrame, short_ma: int = 10, long_ma: int = 50,
                               rsi_period: int = 14, stoch_k: int = 14, stoch_d: int = 3) -> int:
        """
        Chiến lược kết hợp nhiều chỉ báo
        """
        if len(df) < max(long_ma, rsi_period, stoch_k):
            return 0
        
        current_row = df.iloc[-1]
        
        # Moving average trend
        sma_short = current_row[f'sma_{short_ma}']
        sma_long = current_row[f'sma_{long_ma}']
        price = current_row['close']
        
        # Oscillators
        rsi = current_row['rsi']
        stoch_k_val = current_row['stoch_k']
        stoch_d_val = current_row['stoch_d']
        
        # Volume confirmation
        volume_ratio = current_row['volume_ratio']
        
        # Scoring system
        score = 0
        
        # Trend signals
        if price > sma_short > sma_long:
            score += 2
        elif price < sma_short < sma_long:
            score -= 2
        
        # RSI signals
        if 30 < rsi < 70:  # Not extreme
            if rsi > 50:
                score += 1
            else:
                score -= 1
        
        # Stochastic signals
        if stoch_k_val > stoch_d_val and stoch_k_val < 80:
            score += 1
        elif stoch_k_val < stoch_d_val and stoch_k_val > 20:
            score -= 1
        
        # Volume confirmation
        if volume_ratio > 1.2:
            score = score * 1.5 if score > 0 else score * 1.5
        
        # Decision
        if score >= 3:
            return 1  # Strong buy
        elif score <= -3:
            return -1  # Strong sell
        
        return 0  # Hold
    
    @staticmethod
    def adaptive_strategy(df: pd.DataFrame, volatility_threshold: float = 0.02,
                         trend_strength_period: int = 20) -> int:
        """
        Chiến lược thích ứng với điều kiện thị trường
        """
        if len(df) < trend_strength_period:
            return 0
        
        current_row = df.iloc[-1]
        
        # Market regime detection
        volatility = df['close'].pct_change().rolling(20).std().iloc[-1]
        
        # Trend strength
        price_change = (df['close'].iloc[-1] - df['close'].iloc[-trend_strength_period]) / df['close'].iloc[-trend_strength_period]
        
        if volatility > volatility_threshold:
            # High volatility - use mean reversion
            return TradingStrategies.mean_reversion_bollinger(df)
        else:
            # Low volatility - use trend following
            if abs(price_change) > 0.1:  # Strong trend
                return TradingStrategies.trend_following_ema(df)
            else:
                # Sideways market - use breakout
                return TradingStrategies.breakout_strategy(df)

def get_strategy_function(strategy_name: str):
    """
    Trả về hàm chiến lược theo tên
    """
    strategies = {
        'trend_following_ema': TradingStrategies.trend_following_ema,
        'mean_reversion_bollinger': TradingStrategies.mean_reversion_bollinger,
        'momentum_macd_rsi': TradingStrategies.momentum_macd_rsi,
        'breakout_strategy': TradingStrategies.breakout_strategy,
        'multi_timeframe_strategy': TradingStrategies.multi_timeframe_strategy,
        'adaptive_strategy': TradingStrategies.adaptive_strategy
    }
    
    return strategies.get(strategy_name, TradingStrategies.trend_following_ema)

def optimize_strategy_parameters(df: pd.DataFrame, strategy_name: str, 
                               parameter_ranges: Dict[str, list]) -> Dict[str, Any]:
    """
    Tối ưu hóa tham số chiến lược bằng grid search
    """
    from itertools import product
    from backtesting_engine import BacktestingEngine
    
    best_params = {}
    best_performance = -float('inf')
    results = []
    
    # Generate all parameter combinations
    param_names = list(parameter_ranges.keys())
    param_values = list(parameter_ranges.values())
    
    strategy_func = get_strategy_function(strategy_name)
    
    for combination in product(*param_values):
        params = dict(zip(param_names, combination))
        
        # Run backtest
        engine = BacktestingEngine()
        try:
            performance = engine.run_backtest(df, strategy_func, **params)
            
            # Use Sharpe ratio as optimization metric
            metric = performance.get('sharpe_ratio', -float('inf'))
            
            results.append({
                'params': params.copy(),
                'performance': performance,
                'metric': metric
            })
            
            if metric > best_performance:
                best_performance = metric
                best_params = params.copy()
                
        except Exception as e:
            print(f"Error with params {params}: {e}")
            continue
    
    return {
        'best_params': best_params,
        'best_performance': best_performance,
        'all_results': results
    }
