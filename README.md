# 🚀 ETHEREUM TRADING STRATEGY SYSTEM

Hệ thống trading tự động cho ETHUSDT sử dụng các chỉ báo kỹ thuật và machine learning để tối ưu hóa lợi nhuận.

## 📊 KẾT QUẢ CHÍNH

- **💰 Lợi nhuận**: 78.11% trong 2.6 năm (24.68%/năm)
- **📈 Sharpe Ratio**: 0.57
- **📉 Max Drawdown**: -41.78%
- **🎯 Win Rate**: 35.71%
- **🔄 Số giao dịch**: 29 trades
- **💵 Vốn cuối**: $178,110 (từ $100,000)

## 🏗️ CẤU TRÚC DỰ ÁN

```
📁 BlueZone_2nd_test/
├── 📄 README.md                           # Hướng dẫn này
├── 📄 FINAL_TRADING_STRATEGY_REPORT.md    # Báo cáo chi tiết
├── 📄 getData.ipynb                       # Notebook gốc
├── 🐍 simple_data_loader.py               # Thu thập dữ liệu
├── 🐍 technical_indicators.py             # Chỉ báo kỹ thuật
├── 🐍 trading_strategies.py               # Các chiến lược
├── 🐍 backtesting_engine.py               # Engine backtest
├── 🐍 main_trading_system.py              # Hệ thống chính
├── 🐍 run_optimal_strategy.py             # Demo chiến lược tối ưu
├── 📊 ethusdt_extended_data.csv           # Dữ liệu ETHUSDT
└── 📋 trading_strategy_report.json        # Kết quả JSON
```

## 🚀 CÁCH SỬ DỤNG

### 1. Cài đặt dependencies
```bash
pip install pandas numpy requests matplotlib seaborn
```

### 2. Thu thập dữ liệu
```bash
python simple_data_loader.py
```

### 3. Chạy demo chiến lược tối ưu
```bash
python run_optimal_strategy.py
```

### 4. Chạy hệ thống đầy đủ (tùy chọn)
```bash
python main_trading_system.py
```

## 📈 CHIẾN LƯỢC TỐI ƯU: MULTI-TIMEFRAME

### Mô tả
Kết hợp nhiều chỉ báo với hệ thống scoring:
- **Moving Averages**: SMA 10 vs SMA 50
- **RSI**: Lọc tín hiệu (14 periods)
- **Stochastic**: Xác nhận momentum (12,3)
- **Volume**: Xác nhận với volume ratio

### Tham số tối ưu
```python
{
    'short_ma': 10,
    'long_ma': 50,
    'rsi_period': 14,
    'stoch_k': 12,
    'stoch_d': 3
}
```

### Logic giao dịch
```python
# Tính điểm từ các chỉ báo
score = 0

# Trend signals (±2 điểm)
if price > sma_short > sma_long:
    score += 2
elif price < sma_short < sma_long:
    score -= 2

# RSI signals (±1 điểm)
if 30 < rsi < 70:
    score += 1 if rsi > 50 else -1

# Stochastic signals (±1 điểm)
if stoch_k > stoch_d and stoch_k < 80:
    score += 1
elif stoch_k < stoch_d and stoch_k > 20:
    score -= 1

# Volume confirmation (x1.5 multiplier)
if volume_ratio > 1.2:
    score *= 1.5

# Quyết định
if score >= 3: return BUY
elif score <= -3: return SELL
else: return HOLD
```

## 📊 CÁC CHỈ BÁO ĐƯỢC SỬ DỤNG

### Moving Averages
- SMA: 10, 20, 50, 200 periods
- EMA: 10, 20, 50, 200 periods

### Oscillators
- RSI (14)
- Stochastic (14, 3)
- Williams %R (14)
- CCI (20)

### Trend & Volatility
- MACD (12, 26, 9)
- Bollinger Bands (20, 2)
- ATR (14)
- VWAP

### Volume
- Volume SMA (20)
- Volume Ratio

## 🎯 PERFORMANCE METRICS

### Training Period (2020-2022)
- **Total Return**: 838.04%
- **Sharpe Ratio**: 1.58
- **Max Drawdown**: -41.87%
- **Win Rate**: 58.33%

### Test Period (2023-2025)
- **Total Return**: 78.11%
- **Sharpe Ratio**: 0.57
- **Max Drawdown**: -41.78%
- **Win Rate**: 35.71%

## ⚠️ RỦI RO VÀ HẠN CHẾ

### Rủi ro
- **Market regime change**: Strategy có thể không hiệu quả trong bear market
- **Overfitting**: Tham số có thể được tối ưu quá mức
- **Execution risk**: Slippage và fees thực tế
- **Black swan events**: Các sự kiện bất thường

### Hạn chế
- **Underperform trong bull market**: Bỏ lỡ nhiều upside
- **High drawdown**: -41.78% có thể gây stress
- **Low win rate**: Chỉ 35.71% trades có lãi
- **Lag signals**: MA có độ trễ

## 🔧 CẢI TIẾN ĐỀ XUẤT

### 1. Risk Management
- Stop Loss: 2x ATR
- Take Profit: Risk:Reward 1:2
- Position Sizing: Kelly Criterion

### 2. Signal Enhancement
- Volume filter: > 1.5x average
- Multiple timeframes: 4h + 1d
- Market regime detection

### 3. Alternative Strategies
- DCA trong downtrend
- Grid trading trong sideways
- Momentum trong uptrend

## 📞 LIÊN HỆ VÀ HỖ TRỢ

Nếu có câu hỏi hoặc cần hỗ trợ:
1. Đọc `FINAL_TRADING_STRATEGY_REPORT.md` để hiểu chi tiết
2. Kiểm tra code comments trong các file .py
3. Chạy `run_optimal_strategy.py` để xem demo

## ⚖️ DISCLAIMER

- Đây là hệ thống demo cho mục đích giáo dục
- Không phải lời khuyên đầu tư
- Luôn test với số tiền nhỏ trước khi scale up
- Crypto trading có rủi ro cao, có thể mất toàn bộ vốn

---

**🎉 Chúc bạn trading thành công! 🎉**
