import pandas as pd
import numpy as np
import requests
import datetime
import time

def get_extended_ethusdt_data():
    """
    Lấy dữ liệu ETHUSDT mở rộng từ 2020 đến nay
    """
    # S<PERSON> dụng hàm từ getData.ipynb
    def get_data(ticker='ETHUSDT', interval='1d', start='2020-01-01 00:00:00', end=None):
        if end is None:
            end = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
        columns = ['open_time','open', 'high', 'low', 'close', 'volume','close_time', 'qav','num_trades','taker_base_vol','taker_quote_vol', 'ignore']
        usecols=['open', 'high', 'low', 'close', 'volume', 'qav','num_trades','taker_base_vol','taker_quote_vol']
        start_ts = int(datetime.datetime.timestamp(pd.to_datetime(start))*1000)
        end_ts = int(datetime.datetime.timestamp(pd.to_datetime(end))*1000)
        
        df = pd.DataFrame()
        print(f'Downloading {interval} {ticker} ohlc-data from {start} to {end}...', end=' ')
        
        current_start = start_ts
        while current_start < end_ts:
            try:
                url = f'https://api.binance.com/api/v3/klines?symbol={ticker}&interval={interval}&limit=1000&startTime={current_start}&endTime={end_ts}'
                response = requests.get(url, headers={'Cache-Control': 'no-cache', "Pragma": "no-cache"})
                
                if response.status_code != 200:
                    print(f"Error: {response.status_code}")
                    break
                    
                data = pd.DataFrame(response.json(), columns=columns, dtype=np.float64)
                
                if data.empty:
                    break
                    
                current_start = int(data.open_time.tolist()[-1]) + 1
                data.index = [pd.to_datetime(x, unit='ms').strftime('%Y-%m-%d %H:%M:%S') for x in data.open_time]
                data = data[usecols]
                df = pd.concat([df, data], axis=0)
                
                print(f"Downloaded {len(data)} records, total: {len(df)}")
                time.sleep(0.1)  # Rate limiting
                
                if len(data) < 1000:  # Last batch
                    break
                    
            except Exception as e:
                print(f"Error: {e}")
                time.sleep(1)
                continue
        
        print('Done.')
        df.index = pd.to_datetime(df.index)
        df = df.loc[:end]
        return df[['open', 'high', 'low', 'close','volume']]
    
    return get_data()

def create_sample_data():
    """
    Tạo dữ liệu mẫu để test nếu không thể download
    """
    print("Creating sample data for testing...")
    
    # Tạo dữ liệu giả lập từ 2020-01-01 đến 2025-01-01
    dates = pd.date_range(start='2020-01-01', end='2025-01-01', freq='D')
    
    # Simulate ETH price movement
    np.random.seed(42)
    initial_price = 130  # ETH price around Jan 2020
    
    # Generate realistic price data with trend and volatility
    returns = np.random.normal(0.001, 0.03, len(dates))  # Daily returns
    prices = [initial_price]
    
    for i in range(1, len(dates)):
        # Add some trend and mean reversion
        trend = 0.0005 if i < len(dates) * 0.7 else -0.0002  # Bull market then bear
        mean_reversion = -0.1 * (prices[-1] / initial_price - 1) * 0.01
        
        new_return = returns[i] + trend + mean_reversion
        new_price = prices[-1] * (1 + new_return)
        prices.append(max(new_price, 50))  # Minimum price floor
    
    # Create OHLCV data
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # Generate realistic OHLC from close price
        volatility = np.random.uniform(0.01, 0.05)
        
        high = price * (1 + np.random.uniform(0, volatility))
        low = price * (1 - np.random.uniform(0, volatility))
        open_price = prices[i-1] if i > 0 else price
        
        # Ensure OHLC relationships are correct
        high = max(high, open_price, price)
        low = min(low, open_price, price)
        
        volume = np.random.uniform(100000, 1000000)  # Random volume
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': price,
            'volume': volume
        })
    
    df = pd.DataFrame(data, index=dates)
    return df

if __name__ == "__main__":
    try:
        # Try to get real data
        df = get_extended_ethusdt_data()
        print(f"Successfully downloaded {len(df)} records")
        df.to_csv('ethusdt_extended_data.csv')
        print("Data saved to ethusdt_extended_data.csv")
        
    except Exception as e:
        print(f"Failed to download real data: {e}")
        print("Using sample data instead...")
        
        # Use sample data
        df = create_sample_data()
        df.to_csv('ethusdt_sample_data.csv')
        print("Sample data saved to ethusdt_sample_data.csv")
    
    print(f"Data shape: {df.shape}")
    print(f"Date range: {df.index.min()} to {df.index.max()}")
    print("\nFirst few rows:")
    print(df.head())
    print("\nLast few rows:")
    print(df.tail())
