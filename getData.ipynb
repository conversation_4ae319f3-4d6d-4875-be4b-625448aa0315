{"cells": [{"cell_type": "code", "execution_count": 4, "id": "7dfe1cf2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import requests\n", "import datetime"]}, {"cell_type": "code", "execution_count": 5, "id": "e98436d6", "metadata": {}, "outputs": [], "source": ["def get_data(ticker='ETHUSDT', interval='1d', start='2024-08-22 00:00:00', end='2025-02-09 00:00:00'):\n", "    \"\"\"\n", "    interval: str tick interval - 4h/1h/1d ...\n", "    \"\"\"\n", "    columns = ['open_time','open', 'high', 'low', 'close', 'volume','close_time', 'qav','num_trades','taker_base_vol','taker_quote_vol', 'ignore']\n", "    usecols=['open', 'high', 'low', 'close', 'volume', 'qav','num_trades','taker_base_vol','taker_quote_vol']\n", "    start = int(datetime.datetime.timestamp(pd.to_datetime(start))*1000)\n", "    end_u = int(datetime.datetime.timestamp(pd.to_datetime(end))*1000)\n", "    df = pd.DataFrame()\n", "    print(f'Downloading {interval} {ticker} ohlc-data ...', end=' ')\n", "    while True:\n", "        url = f'https://www.binance.com/api/v3/klines?symbol={ticker}&interval={interval}&limit=1000&startTime={start}#&endTime={end_u}'\n", "        data = pd.DataFrame(requests.get(url, headers={'Cache-Control': 'no-cache', \"Pragma\": \"no-cache\"}).json(), columns=columns, dtype=np.float64)    \n", "        start = int(data.open_time.tolist()[-1])+1\n", "        data.index = [pd.to_datetime(x, unit='ms').strftime('%Y-%m-%d %H:%M:%S') for x in data.open_time]\n", "        data = data[usecols]\n", "        df = pd.concat([df, data], axis=0)\n", "        if end in data.index.tolist():\n", "            break\n", "    print('Done.')\n", "    df.index = pd.to_datetime(df.index)\n", "    df = df.loc[:end]\n", "    return df[['open', 'high', 'low', 'close','volume']]\n"]}, {"cell_type": "code", "execution_count": 6, "id": "340f3a82", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading 1d ETHUSDT ohlc-data ... Done.\n"]}], "source": ["df = get_data()"]}, {"cell_type": "code", "execution_count": 7, "id": "3139cb67", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-08-22</th>\n", "      <td>2630.71</td>\n", "      <td>2644.69</td>\n", "      <td>2584.20</td>\n", "      <td>2622.88</td>\n", "      <td>199063.1196</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-23</th>\n", "      <td>2622.89</td>\n", "      <td>2799.13</td>\n", "      <td>2621.40</td>\n", "      <td>2762.48</td>\n", "      <td>401786.4447</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-24</th>\n", "      <td>2762.49</td>\n", "      <td>2820.00</td>\n", "      <td>2731.26</td>\n", "      <td>2768.00</td>\n", "      <td>263542.9441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-25</th>\n", "      <td>2768.01</td>\n", "      <td>2792.28</td>\n", "      <td>2733.21</td>\n", "      <td>2746.13</td>\n", "      <td>154527.2352</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-26</th>\n", "      <td>2746.12</td>\n", "      <td>2762.00</td>\n", "      <td>2666.66</td>\n", "      <td>2680.49</td>\n", "      <td>211708.1247</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-05</th>\n", "      <td>2731.19</td>\n", "      <td>2826.95</td>\n", "      <td>2699.13</td>\n", "      <td>2788.25</td>\n", "      <td>686593.0429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-06</th>\n", "      <td>2788.25</td>\n", "      <td>2857.64</td>\n", "      <td>2655.28</td>\n", "      <td>2686.64</td>\n", "      <td>719459.9571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-07</th>\n", "      <td>2686.63</td>\n", "      <td>2797.50</td>\n", "      <td>2562.51</td>\n", "      <td>2622.10</td>\n", "      <td>695467.3612</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-08</th>\n", "      <td>2622.11</td>\n", "      <td>2667.30</td>\n", "      <td>2588.80</td>\n", "      <td>2632.46</td>\n", "      <td>379685.1509</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-09</th>\n", "      <td>2632.46</td>\n", "      <td>2698.90</td>\n", "      <td>2520.02</td>\n", "      <td>2627.18</td>\n", "      <td>387166.7911</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>172 rows × 5 columns</p>\n", "</div>"], "text/plain": ["               open     high      low    close       volume\n", "2024-08-22  2630.71  2644.69  2584.20  2622.88  199063.1196\n", "2024-08-23  2622.89  2799.13  2621.40  2762.48  401786.4447\n", "2024-08-24  2762.49  2820.00  2731.26  2768.00  263542.9441\n", "2024-08-25  2768.01  2792.28  2733.21  2746.13  154527.2352\n", "2024-08-26  2746.12  2762.00  2666.66  2680.49  211708.1247\n", "...             ...      ...      ...      ...          ...\n", "2025-02-05  2731.19  2826.95  2699.13  2788.25  686593.0429\n", "2025-02-06  2788.25  2857.64  2655.28  2686.64  719459.9571\n", "2025-02-07  2686.63  2797.50  2562.51  2622.10  695467.3612\n", "2025-02-08  2622.11  2667.30  2588.80  2632.46  379685.1509\n", "2025-02-09  2632.46  2698.90  2520.02  2627.18  387166.7911\n", "\n", "[172 rows x 5 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}