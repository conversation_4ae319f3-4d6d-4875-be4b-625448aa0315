import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class BacktestingEngine:
    """
    Engine để backtest các chiến lược trading
    """
    
    def __init__(self, initial_capital: float = 100000, commission: float = 0.001):
        """
        Args:
            initial_capital: <PERSON><PERSON><PERSON> ban đầu (USD)
            commission: <PERSON><PERSON> giao dịch (0.1% = 0.001)
        """
        self.initial_capital = initial_capital
        self.commission = commission
        self.reset()
    
    def reset(self):
        """Reset trạng thái backtesting"""
        self.capital = self.initial_capital
        self.position = 0  # Số lượng ETH đang hold
        self.trades = []
        self.portfolio_values = []
        self.positions = []
        self.cash_values = []
        
    def execute_trade(self, price: float, signal: int, timestamp: pd.Timestamp, 
                     position_size: float = 1.0, stop_loss: Optional[float] = None,
                     take_profit: Optional[float] = None):
        """
        Thực hiện giao dịch
        
        Args:
            price: <PERSON><PERSON><PERSON> hiện tại
            signal: 1 (buy), -1 (sell), 0 (hold)
            timestamp: Thời gian giao dịch
            position_size: Tỷ lệ vốn sử dụng (0-1)
            stop_loss: Mức cắt lỗ (% từ giá mua)
            take_profit: Mức chốt lời (% từ giá mua)
        """
        if signal == 1 and self.position <= 0:  # Buy signal
            # Tính số lượng ETH có thể mua
            available_capital = self.capital * position_size
            commission_cost = available_capital * self.commission
            net_capital = available_capital - commission_cost
            quantity = net_capital / price
            
            if quantity > 0:
                self.position += quantity
                self.capital -= available_capital
                
                self.trades.append({
                    'timestamp': timestamp,
                    'type': 'BUY',
                    'price': price,
                    'quantity': quantity,
                    'commission': commission_cost,
                    'capital_after': self.capital,
                    'position_after': self.position
                })
        
        elif signal == -1 and self.position > 0:  # Sell signal
            # Bán toàn bộ position
            sell_value = self.position * price
            commission_cost = sell_value * self.commission
            net_proceeds = sell_value - commission_cost
            
            self.capital += net_proceeds
            
            self.trades.append({
                'timestamp': timestamp,
                'type': 'SELL',
                'price': price,
                'quantity': self.position,
                'commission': commission_cost,
                'capital_after': self.capital,
                'position_after': 0
            })
            
            self.position = 0
    
    def calculate_portfolio_value(self, current_price: float) -> float:
        """Tính giá trị portfolio hiện tại"""
        return self.capital + (self.position * current_price)
    
    def run_backtest(self, df: pd.DataFrame, strategy_func, **strategy_params) -> Dict:
        """
        Chạy backtest với chiến lược cho trước
        
        Args:
            df: DataFrame với dữ liệu và chỉ báo
            strategy_func: Hàm chiến lược trả về signal
            **strategy_params: Tham số cho chiến lược
        
        Returns:
            Dictionary với kết quả backtest
        """
        self.reset()
        
        for i, (timestamp, row) in enumerate(df.iterrows()):
            # Tính signal từ chiến lược
            signal = strategy_func(df.iloc[:i+1], **strategy_params)
            
            # Thực hiện giao dịch
            if signal != 0:
                self.execute_trade(row['close'], signal, timestamp)
            
            # Lưu trạng thái portfolio
            portfolio_value = self.calculate_portfolio_value(row['close'])
            self.portfolio_values.append(portfolio_value)
            self.positions.append(self.position)
            self.cash_values.append(self.capital)
        
        return self.calculate_performance_metrics(df)
    
    def calculate_performance_metrics(self, df: pd.DataFrame) -> Dict:
        """Tính toán các chỉ số hiệu suất"""
        if not self.portfolio_values:
            return {}
        
        portfolio_series = pd.Series(self.portfolio_values, index=df.index)
        returns = portfolio_series.pct_change().dropna()
        
        # Basic metrics
        total_return = (portfolio_series.iloc[-1] / self.initial_capital - 1) * 100
        annualized_return = ((portfolio_series.iloc[-1] / self.initial_capital) ** (365 / len(df)) - 1) * 100
        
        # Risk metrics
        volatility = returns.std() * np.sqrt(365) * 100
        sharpe_ratio = (annualized_return - 2) / volatility if volatility > 0 else 0  # Assuming 2% risk-free rate
        
        # Drawdown
        rolling_max = portfolio_series.expanding().max()
        drawdown = (portfolio_series - rolling_max) / rolling_max * 100
        max_drawdown = drawdown.min()
        
        # Win rate
        winning_trades = 0
        total_trades = 0
        if len(self.trades) >= 2:
            for i in range(1, len(self.trades), 2):  # Pairs of buy-sell
                if i < len(self.trades):
                    buy_price = self.trades[i-1]['price']
                    sell_price = self.trades[i]['price']
                    if sell_price > buy_price:
                        winning_trades += 1
                    total_trades += 1
        
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        # Buy and hold comparison
        buy_hold_return = (df['close'].iloc[-1] / df['close'].iloc[0] - 1) * 100
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': len(self.trades),
            'buy_hold_return': buy_hold_return,
            'excess_return': total_return - buy_hold_return,
            'final_portfolio_value': portfolio_series.iloc[-1],
            'portfolio_series': portfolio_series,
            'trades': self.trades
        }

class PositionSizing:
    """Các phương pháp quản lý vốn"""
    
    @staticmethod
    def fixed_percentage(capital: float, percentage: float = 0.1) -> float:
        """Sử dụng tỷ lệ cố định của vốn"""
        return min(percentage, 1.0)
    
    @staticmethod
    def kelly_criterion(win_rate: float, avg_win: float, avg_loss: float) -> float:
        """Kelly Criterion cho position sizing"""
        if avg_loss <= 0:
            return 0
        b = avg_win / abs(avg_loss)  # Tỷ lệ win/loss
        p = win_rate / 100  # Xác suất thắng
        kelly = (b * p - (1 - p)) / b
        return max(0, min(kelly, 0.25))  # Giới hạn tối đa 25%
    
    @staticmethod
    def volatility_based(volatility: float, target_vol: float = 0.15) -> float:
        """Position sizing dựa trên volatility"""
        if volatility <= 0:
            return 0.1
        return min(target_vol / volatility, 1.0)

class RiskManagement:
    """Quản lý rủi ro"""
    
    @staticmethod
    def calculate_stop_loss(entry_price: float, atr: float, multiplier: float = 2.0) -> float:
        """Tính stop loss dựa trên ATR"""
        return entry_price - (atr * multiplier)
    
    @staticmethod
    def calculate_take_profit(entry_price: float, atr: float, multiplier: float = 3.0) -> float:
        """Tính take profit dựa trên ATR"""
        return entry_price + (atr * multiplier)
    
    @staticmethod
    def trailing_stop(current_price: float, highest_price: float, trail_percent: float = 0.05) -> float:
        """Trailing stop loss"""
        return highest_price * (1 - trail_percent)
