"""
DEMO: <PERSON><PERSON><PERSON> chiến lược tối ưu ETHUSDT
Chiến lược: Multi-timeframe Strategy với tham số đã được tối ưu hóa
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# Import modules
from technical_indicators import calculate_all_indicators
from backtesting_engine import BacktestingEngine
from trading_strategies import TradingStrategies

def load_data():
    """Load dữ liệu ETHUSDT"""
    try:
        df = pd.read_csv('ethusdt_extended_data.csv', index_col=0, parse_dates=True)
        print(f"✅ Loaded {len(df)} records from ethusdt_extended_data.csv")
        return df
    except FileNotFoundError:
        print("❌ Data file not found. Please run simple_data_loader.py first.")
        return None

def run_optimal_strategy():
    """Chạy chiến lược tối ưu"""
    print("🚀 ETHEREUM OPTIMAL TRADING STRATEGY DEMO")
    print("=" * 50)
    
    # Load data
    data = load_data()
    if data is None:
        return
    
    # Calculate indicators
    print("📊 Calculating technical indicators...")
    data_with_indicators = calculate_all_indicators(data)
    
    # Split data
    split_date = '2023-01-01'
    train_data = data_with_indicators[data_with_indicators.index < split_date]
    test_data = data_with_indicators[data_with_indicators.index >= split_date]
    
    print(f"📈 Train period: {train_data.index.min()} to {train_data.index.max()} ({len(train_data)} days)")
    print(f"📉 Test period: {test_data.index.min()} to {test_data.index.max()} ({len(test_data)} days)")
    
    # Optimal parameters (from optimization)
    optimal_params = {
        'short_ma': 10,
        'long_ma': 50,
        'rsi_period': 14,
        'stoch_k': 12,
        'stoch_d': 3
    }
    
    print(f"⚙️ Using optimal parameters: {optimal_params}")
    
    # Run backtest on test data
    print("\n🔄 Running backtest on out-of-sample data...")
    engine = BacktestingEngine(initial_capital=100000)
    
    performance = engine.run_backtest(
        test_data, 
        TradingStrategies.multi_timeframe_strategy,
        **optimal_params
    )
    
    # Display results
    print("\n📊 PERFORMANCE RESULTS")
    print("=" * 30)
    print(f"💰 Initial Capital: ${engine.initial_capital:,.2f}")
    print(f"💰 Final Portfolio Value: ${performance['final_portfolio_value']:,.2f}")
    print(f"📈 Total Return: {performance['total_return']:.2f}%")
    print(f"📈 Annualized Return: {performance['annualized_return']:.2f}%")
    print(f"📊 Sharpe Ratio: {performance['sharpe_ratio']:.2f}")
    print(f"📉 Max Drawdown: {performance['max_drawdown']:.2f}%")
    print(f"🎯 Win Rate: {performance['win_rate']:.2f}%")
    print(f"🔄 Total Trades: {performance['total_trades']}")
    print(f"📊 Volatility: {performance['volatility']:.2f}%")
    
    # Compare with Buy & Hold
    print(f"\n🔄 Buy & Hold Return: {performance['buy_hold_return']:.2f}%")
    print(f"⚡ Excess Return: {performance['excess_return']:.2f}%")
    
    # Show recent trades
    if performance['trades']:
        print(f"\n📋 RECENT TRADES (Last 5)")
        print("-" * 50)
        recent_trades = performance['trades'][-5:]
        for trade in recent_trades:
            timestamp_str = str(trade['timestamp'])[:10]
            print(f"{timestamp_str} | {trade['type']:4} | ${trade['price']:7.2f} | Qty: {trade['quantity']:.2f}")
    
    # Plot results
    plot_results(performance, test_data)
    
    return performance

def plot_results(performance, test_data):
    """Vẽ biểu đồ kết quả"""
    try:
        portfolio_series = performance['portfolio_series']
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Portfolio value over time
        ax1.plot(portfolio_series.index, portfolio_series.values, label='Strategy', linewidth=2)
        ax1.set_title('Portfolio Value Over Time')
        ax1.set_ylabel('Portfolio Value ($)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Price vs Strategy performance
        price_normalized = (test_data['close'] / test_data['close'].iloc[0]) * 100000
        ax2.plot(price_normalized.index, price_normalized.values, label='Buy & Hold', alpha=0.7)
        ax2.plot(portfolio_series.index, portfolio_series.values, label='Strategy', alpha=0.7)
        ax2.set_title('Strategy vs Buy & Hold')
        ax2.set_ylabel('Value ($)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Drawdown
        rolling_max = portfolio_series.expanding().max()
        drawdown = (portfolio_series - rolling_max) / rolling_max * 100
        ax3.fill_between(drawdown.index, drawdown.values, 0, color='red', alpha=0.3)
        ax3.plot(drawdown.index, drawdown.values, color='red', linewidth=1)
        ax3.set_title('Drawdown')
        ax3.set_ylabel('Drawdown (%)')
        ax3.grid(True, alpha=0.3)
        
        # Returns distribution
        returns = portfolio_series.pct_change().dropna() * 100
        ax4.hist(returns, bins=30, alpha=0.7, edgecolor='black')
        ax4.axvline(returns.mean(), color='red', linestyle='--', label=f'Mean: {returns.mean():.2f}%')
        ax4.set_title('Daily Returns Distribution')
        ax4.set_xlabel('Daily Returns (%)')
        ax4.set_ylabel('Frequency')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('strategy_performance.png', dpi=300, bbox_inches='tight')
        print(f"\n📊 Charts saved to 'strategy_performance.png'")
        plt.show()
        
    except Exception as e:
        print(f"⚠️ Could not create plots: {e}")

def get_current_signal(data_with_indicators):
    """Lấy tín hiệu hiện tại"""
    optimal_params = {
        'short_ma': 10,
        'long_ma': 50,
        'rsi_period': 14,
        'stoch_k': 12,
        'stoch_d': 3
    }
    
    signal = TradingStrategies.multi_timeframe_strategy(
        data_with_indicators, 
        **optimal_params
    )
    
    current_price = data_with_indicators['close'].iloc[-1]
    current_date = data_with_indicators.index[-1]
    
    signal_text = {1: "🟢 BUY", -1: "🔴 SELL", 0: "⚪ HOLD"}
    
    print(f"\n🎯 CURRENT SIGNAL")
    print(f"Date: {current_date.strftime('%Y-%m-%d')}")
    print(f"Price: ${current_price:.2f}")
    print(f"Signal: {signal_text.get(signal, 'UNKNOWN')}")
    
    return signal

if __name__ == "__main__":
    # Run the optimal strategy
    performance = run_optimal_strategy()
    
    if performance:
        # Get current signal
        data = load_data()
        if data is not None:
            data_with_indicators = calculate_all_indicators(data)
            get_current_signal(data_with_indicators)
    
    print(f"\n✅ Demo completed! Check 'strategy_performance.png' for charts.")
    print(f"📄 Full report available in 'FINAL_TRADING_STRATEGY_REPORT.md'")
